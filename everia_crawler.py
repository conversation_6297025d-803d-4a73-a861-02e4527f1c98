import asyncio
import json
import os
import re
import signal
import sys
import time
from functools import partial
from typing import Dict, Any, List, Tuple, Optional
from urllib.parse import urljoin

import aiofiles
import aiofiles.os
import httpx
import uvicorn
from bs4 import BeautifulSoup
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, Response
from loguru import logger
import aiosqlite
try:
    import pyppeteer
    PYPPETEER_AVAILABLE = True
except ImportError:
    PYPPETEER_AVAILABLE = False


# --- 核心配置与全局变量 ---
SAVE_DIR = 'downloaded'
DB_FILE = 'download_state.db'
LOG_FILE = "download_log.txt"
ERROR_LOG_FILE = "error_log.json" # 【新增】错误日志文件名
CATEGORIES: Dict[str, str] = {
    'korea': 'https://everia.club/category/korea/', 'cosplay': 'https://everia.club/category/cosplay/',
    'japan': 'https://everia.club/category/japan/', 'gravure': 'https://everia.club/category/gravure/',
    'chinese': 'https://everia.club/category/chinese/','thailand': 'https://everia.club/category/thailand/',
}
HTTP_TIMEOUT = 30
MAX_CONCURRENT_ALBUMS = 25
PAGE_SCAN_CONCURRENCY = 25
DEFAULT_IMAGE_CONCURRENCY = 15

# 队列大小限制 - 防止内存过度使用
MAX_ALBUM_QUEUE_SIZE = 200  # 增加队列容量
MAX_BROADCAST_QUEUE_SIZE = 1000  # 限制广播队列大小

PYPPETEER_PAGE_TIMEOUT = 20000
PYPPETEER_WAIT_DELAY = 3000

# --- 异步状态管理与通信 ---
CRAWL_RUNNING = asyncio.Event()
FORCE_STOP = asyncio.Event()  # 强制停止标志
broadcast_queue = asyncio.Queue(maxsize=MAX_BROADCAST_QUEUE_SIZE)
stats_update_queue = asyncio.Queue()
active_tasks = set()  # 跟踪活跃任务
shared_state: Dict[str, Any] = {
    "progress": {}, "stats": {"totalAlbums": 0, "totalImages": 0, "totalSize": 0, "speed": 0.0},
    "status": "空闲", "is_crawling": False, "is_organizing": False,
    "current_category": "", "concurrency": DEFAULT_IMAGE_CONCURRENCY,
    "page_concurrency": PAGE_SCAN_CONCURRENCY,
    "lock": asyncio.Lock(),
    "browser": None
}
# 【新增】用于防止并发写错误日志文件的锁
error_log_lock = asyncio.Lock()

# 信号处理器
def signal_handler(signum, frame):
    """处理 Ctrl+C 等信号"""
    logger.warning(f"收到信号 {signum}，正在强制停止所有任务...")
    FORCE_STOP.set()
    CRAWL_RUNNING.clear()

    # 取消所有活跃任务
    for task in active_tasks.copy():
        if not task.done():
            task.cancel()

    # 给一些时间让任务清理
    import time
    time.sleep(2)

    logger.info("强制退出程序")
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# --- 日志配置 ---
logger.remove()
logger.add(sys.stdout, level="INFO", format="<green>{time:HH:mm:ss}</green>| <level>{level: <7}</level>| <level></level>")
logger.add(LOG_FILE, encoding='utf-8', level="DEBUG", rotation="10 MB")

async def log_to_ui(message: str, m_type: str = "info"):
    color_map = {"info": "#e0e0e0", "success": "#4caf50", "error": "#f44336", "warning": "#ff9800", "primary": "#00bcd4"}
    html_message = f'<span style="color:{color_map.get(m_type, "#e0e0e0")};">{time.strftime("%H:%M:%S")} | {message}</span>'
    plain_message = f"{time.strftime('%H:%M:%S')} | {re.sub('<[^<]+?>', '', message)}"

    # 非阻塞发送，如果队列满了就丢弃旧消息
    try:
        broadcast_queue.put_nowait({"type": "log", "html": html_message, "plain": plain_message})
    except asyncio.QueueFull:
        # 队列满了，尝试丢弃一些旧消息
        try:
            for _ in range(10):  # 丢弃最多10个旧消息
                broadcast_queue.get_nowait()
                broadcast_queue.task_done()
            broadcast_queue.put_nowait({"type": "log", "html": html_message, "plain": plain_message})
        except asyncio.QueueEmpty:
            pass  # 队列已空，忽略

# --- WebSocket 管理器 ---
class WebSocketManager:
    def __init__(self): self.active_connections: list[WebSocket] = []
    async def connect(self, ws: WebSocket): await ws.accept(); self.active_connections.append(ws)
    def disconnect(self, ws: WebSocket): self.active_connections.remove(ws)
    async def broadcast(self, data: dict):
        if self.active_connections:
            await asyncio.gather(*[c.send_json(data) for c in self.active_connections], return_exceptions=True)

ws_manager = WebSocketManager()
app = FastAPI()

# --- 前端代码 (V16.5) ---
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Everia Crawler V16.5 - 春色写真馆</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGRkI2QzEiIHJ4PSI2Ii8+CiAgPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iMyIgZmlsbD0iI0ZGNjlCNCIvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIuNSIgZmlsbD0iI0ZGMTQ5MyIvPgogIDxjaXJjbGUgY3g9IjE2IiBjeT0iMjAiIHI9IjIiIGZpbGw9IiNEQzE0M0MiLz4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KICA8Y2lyY2xlIGN4PSIyMiIgY3k9IjIwIiByPSIxLjUiIGZpbGw9IiNGRjY5QjQiLz4KPC9zdmc+">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        /* 移除所有列表标记 */
        ul, ol { list-style: none; }
        li::marker { display: none; }

        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Source Han Sans SC', 'Microsoft YaHei', '微软雅黑', sans-serif;
            background: linear-gradient(135deg, #ffeef8 0%, #fff5f0 25%, #f0fff4 50%, #fff8dc 75%, #fdf5e6 100%);
            min-height: 100vh;
            padding: 20px;
            color: #5d4e37;
            line-height: 1.6;
            font-size: 16px;
        }
        .container{max-width:1600px;margin:20px auto;padding:0 20px}
        #loader{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(255, 238, 248, 0.95);z-index:9999;display:flex;justify-content:center;align-items:center;flex-direction:column;transition:opacity .5s}
        #loader.hidden{opacity:0;pointer-events:none}
        .spinner{width:50px;height:50px;border:5px solid rgba(255, 182, 193, 0.3);border-top-color:#ff69b4;border-radius:50%;animation:spin 1s linear infinite}
        #loader-status{margin-top:20px;font-size:1.2em;color:#8b4513}
        @keyframes spin{to{transform:rotate(360deg)}}
        .tabs{display:flex;border-bottom:2px solid rgba(255, 182, 193, 0.3);margin-bottom:20px}
        .tab-button{padding:10px 20px;cursor:pointer;background:none;border:none;color:#5d4e37;font-size:1.1em;opacity:.7;transition:all .3s ease;border-bottom:3px solid transparent}
        .tab-button.active{opacity:1;border-bottom-color:#ff69b4}
        .tab-content{display:none}.tab-content.active{display:block}
        .grid{display:grid;grid-template-columns:1fr 1fr;gap:20px;align-items:start}
        .card{background:rgba(255, 255, 255, 0.9);backdrop-filter:blur(10px);border-radius:20px;padding:30px;box-shadow:0 4px 15px rgba(255, 182, 193, 0.2);border:1px solid rgba(255, 182, 193, 0.1)}
        h1,h2{color:#8b4513;margin-bottom:15px;text-shadow:2px 2px 4px rgba(255, 182, 193, 0.3)}
        button{background:linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);color:#fff;border:none;padding:12px 20px;border-radius:10px;cursor:pointer;font-size:1em;transition:all .3s ease;margin-right:10px;position:relative;box-shadow:0 2px 10px rgba(255, 105, 180, 0.3)}
        button:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 5px 15px rgba(255, 105, 180, 0.4)}
        button:disabled{background:rgba(255, 182, 193, 0.5);cursor:not-allowed;transform:none;box-shadow:none}
        button .btn-text.hidden{visibility:hidden}
        button .btn-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:transparent;border-radius:50%;animation:spin .6s linear infinite}
        button.loading .btn-loader{display:block}
        .log-container{height:400px;overflow-y:scroll;background:rgba(255, 240, 245, 0.7);padding:15px;border-radius:10px;font-family:monospace;font-size:.9em;white-space:pre-wrap;margin-top:20px;border:1px solid rgba(255, 182, 193, 0.3)}
        .stats-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:15px}
        .stat-item{background:rgba(255, 240, 245, 0.7);padding:15px;border-radius:10px;text-align:center;border:1px solid rgba(255, 182, 193, 0.3)}
        .stat-item .label{font-size:.9em;opacity:.7;color:#5d4e37}.stat-item .value{font-size:1.5em;font-weight:700;color:#8b4513}
        .progress-list{max-height:350px;overflow-y:auto;margin-top:20px;padding-right:10px}
        .progress-item .progress-bar-bg{background:rgba(255, 182, 193, 0.2);border-radius:10px;height:20px;overflow:hidden;width:100%;border:1px solid rgba(255, 182, 193, 0.3)}
        .progress-item .progress-bar{background:linear-gradient(90deg, #ff69b4, #ff1493);height:100%;transition:width .2s ease;text-align:center;color:#fff;font-size:.8em;line-height:20px;border-radius:10px}
        .progress-item .progress-label{font-size:.9em;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:5px;color:#5d4e37}

        .category-checklist{display:grid;grid-template-columns:repeat(auto-fill,minmax(150px,1fr));gap:10px;margin-bottom:20px}
        .category-checklist label{background:rgba(255, 240, 245, 0.7);padding:8px 12px;border-radius:10px;cursor:pointer;display:flex;align-items:center;border:1px solid rgba(255, 182, 193, 0.3);transition:all .3s ease}
        .category-checklist label:hover{background:rgba(255, 182, 193, 0.3)}
        .category-checklist input{margin-right:10px}
        #sparkline{stroke:#ff69b4;stroke-width:2;fill:rgba(255, 105, 180, 0.2)}
    </style>
</head>
<body>
<div id="loader"><div class="spinner"></div><p id="loader-status">正在连接服务器...</p></div>
<div id="app" class="container">
    <h1>🌸 春色写真馆 - 爬虫控制台 <span style="font-size: 0.5em; color: #ff69b4;">V16.5</span></h1>
    <div class="tabs"><button class="tab-button active" data-tab="crawler">爬虫控制台</button></div>
    <div id="crawler" class="tab-content active"><div class="grid"><div><div class="card"><h2>控制与设置</h2><div id="category-checklist-container"><h3>选择要爬取的分类</h3><div id="category-checklist" class="category-checklist"></div></div><div style="margin-top:20px;"><button id="start-crawl-btn"><span class="btn-text">开始爬取</span><span class="btn-loader"></span></button><button id="stop-crawl-btn" disabled><span class="btn-text">停止爬取</span><span class="btn-loader"></span></button><button id="organize-files-btn"><span class="btn-text">整理所有文件</span><span class="btn-loader"></span></button></div><div style="margin-top: 20px;"><label for="concurrency-slider">单个图集内部图片并发数: <span id="concurrency-value">15</span></label><input type="range" id="concurrency-slider" min="1" max="100" step="1" value="15" style="width:100%;"></div><div style="margin-top: 15px;"><label for="page-concurrency-slider">页面扫描并发数: <span id="page-concurrency-value">25</span></label><input type="range" id="page-concurrency-slider" min="1" max="50" step="1" value="25" style="width:100%;"></div></div><div class="card" style="margin-top: 20px;"><h2>UI 日志</h2><div id="log-container" class="log-container"></div></div></div><div><div class="card"><h2>实时统计</h2><div id="stats-grid" class="stats-grid"><div class="stat-item"><div class="label">任务状态</div><div class="value" id="stat-status">空闲</div></div><div class="stat-item"><div class="label">当前分类</div><div class="value" id="stat-current-category">N/A</div></div><div class="stat-item"><div class="label">下载图集</div><div class="value" id="stat-total-albums">0</div></div><div class="stat-item"><div class="label">下载图片</div><div class="value" id="stat-total-images">0</div></div><div class="stat-item"><div class="label">总大小</div><div class="value" id="stat-total-size">0 B</div></div><div class="stat-item"><div class="label">下载速率</div><div class="value" id="stat-speed" style="color:var(--success-color);">0 B/s</div></div></div><h3 style="margin-top: 20px;">速率图表 (SVG)</h3><svg id="speed-chart" width="100%" height="100" viewBox="0 0 300 100" preserveAspectRatio="none"><path id="sparkline" d="M0,100 L300,100"/></svg></div><div class="card" style="margin-top: 20px;"><h2>下载队列</h2><div id="progress-list" class="progress-list"><div id="progress-placeholder">暂无下载任务</div></div></div></div></div></div>

</div>
<script>
(() => {
    const $ = (s) => document.querySelector(s);
    const $$ = (s) => document.querySelectorAll(s);

    const dom = {
        loader: $('#loader'), loaderStatus: $('#loader-status'),
        tabs: $$('.tab-button'), tabContents: $$('.tab-content'), logContainer: $('#log-container'),
        startBtn: $('#start-crawl-btn'), stopBtn: $('#stop-crawl-btn'), organizeBtn: $('#organize-files-btn'),
        concurrencySlider: $('#concurrency-slider'), concurrencyValue: $('#concurrency-value'),
        pageConcurrencySlider: $('#page-concurrency-slider'), pageConcurrencyValue: $('#page-concurrency-value'),
        categoryChecklist: $('#category-checklist'),
        stats: { status: $('#stat-status'), currentCategory: $('#stat-current-category'), totalAlbums: $('#stat-total-albums'), totalImages: $('#stat-total-images'), totalSize: $('#stat-total-size'), speed: $('#stat-speed') },
        progressList: $('#progress-list'), progressPlaceholder: $('#progress-placeholder'),

        sparkline: $('#sparkline') };
    
    let ws;
    let state = { isCrawling: false, isOrganizing: false, speedHistory: new Array(30).fill(0) };

    const setButtonLoading = (btn, isLoading) => {
        if (!btn) return;
        // 只有停止按钮才显示加载动画
        if (btn === dom.stopBtn) {
            btn.classList.toggle('loading', isLoading);
            btn.querySelector('.btn-text').classList.toggle('hidden', isLoading);
        }
        const isAnyTaskRunning = state.isCrawling || state.isOrganizing;
        if (btn === dom.stopBtn) {
            btn.disabled = isLoading || !state.isCrawling;
        } else {
            btn.disabled = isAnyTaskRunning || isLoading;
        }
    };
    const formatBytes = (b, d = 2) => { if (!b || b <= 0) return '0 B'; const k = 1024, s = ['B', 'KB', 'MB', 'GB', 'TB'], i = Math.floor(Math.log(b) / Math.log(k)); return `${parseFloat((b / Math.pow(k, i)).toFixed(d))} ${s[i]}` };
    const addLog = (html, plainText) => { dom.logContainer.insertAdjacentHTML('beforeend', `<p>${html}</p>`); if (dom.logContainer.children.length > 500) { dom.logContainer.firstElementChild.remove(); } dom.logContainer.scrollTop = dom.logContainer.scrollHeight; console.log(plainText); };
    const updateSparkline = () => { const max = Math.max(...state.speedHistory, 1), w = 300, h = 100; const p = state.speedHistory.map((d, i) => `${(i / 29) * w},${h - (d / max) * h}`).join(' L '); dom.sparkline.setAttribute('d', `M ${p} L ${w},${h} L 0,${h} Z`) };
    const updateStats = (s) => { dom.stats.totalAlbums.textContent = s.totalAlbums; dom.stats.totalImages.textContent = s.totalImages; dom.stats.totalSize.textContent = formatBytes(s.totalSize); dom.stats.speed.textContent = `${formatBytes(s.speed)}/s`; state.speedHistory.push(s.speed || 0); if(state.speedHistory.length>30)state.speedHistory.shift(); updateSparkline() };
    const updateProgress = (p) => {
        const isEmpty = Object.keys(p).length === 0;
        dom.progressPlaceholder.style.display = isEmpty ? 'flex' : 'none';

        // 如果是空对象，直接清空所有进度项
        if (isEmpty) {
            $$('#progress-list .progress-item').forEach(i => i.remove());
            return;
        }

        // 移除不存在的进度项
        $$('#progress-list .progress-item').forEach(i => {
            if (!p[i.dataset.album]) i.remove()
        });

        // 添加或更新进度项
        for (const n in p) {
            const d = p[n];
            let i = $(`#progress-list .progress-item[data-album="${n}"]`);
            if (!i) {
                i = document.createElement('div');
                i.className = 'progress-item';
                i.dataset.album = n;
                i.innerHTML = `<div class="progress-label"></div><div class="progress-bar-bg"><div class="progress-bar"></div></div>`;
                dom.progressList.appendChild(i)
            }
            const pct = d.total > 0 ? (d.completed / d.total) * 100 : 0;
            i.querySelector('.progress-bar').style.width = `${pct}%`;
            i.querySelector('.progress-label').textContent = `${n} (${d.completed}/${d.total})`
        }
    };
    const setAppBusyState = (isCrawling, isOrganizing) => {
        state.isCrawling = isCrawling; state.isOrganizing = isOrganizing;
        const isAnyTaskRunning = isCrawling || isOrganizing;
        dom.startBtn.disabled = isAnyTaskRunning; dom.organizeBtn.disabled = isAnyTaskRunning;
        dom.concurrencySlider.disabled = isAnyTaskRunning; dom.pageConcurrencySlider.disabled = isAnyTaskRunning;
        dom.stopBtn.disabled = !isCrawling;
        setButtonLoading(dom.startBtn, isCrawling); setButtonLoading(dom.organizeBtn, isOrganizing);
    };
    const updateStatusText = (status, currentCategory) => {
        if (status !== undefined) dom.stats.status.textContent = status;
        if (currentCategory !== undefined) dom.stats.currentCategory.textContent = currentCategory || 'N/A';
    };

    const clearUIState = () => {
        // 清空日志容器
        dom.logContainer.innerHTML = '';
        // 直接清空所有下载队列项
        $$('#progress-list .progress-item').forEach(i => i.remove());
        dom.progressPlaceholder.style.display = 'flex';
        // 重置速率图表
        state.speedHistory = new Array(30).fill(0);
        updateSparkline();
        // 重置当前分类
        updateStatusText('空闲', 'N/A');
    };

    
    const connectWebSocket = () => {
        ws = new WebSocket(`${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`);
        ws.onopen = () => { dom.loaderStatus.textContent = '连接成功，请求初始状态...'; ws.send(JSON.stringify({ action: 'get_initial_state' })); };
        ws.onclose = () => { dom.loader.classList.remove('hidden'); document.body.style.overflow = 'hidden'; dom.loaderStatus.textContent = '与服务器断开连接, 3秒后重连...'; setTimeout(connectWebSocket, 3000) };

        // 添加心跳机制，防止长时间运行时连接超时
        const heartbeat = setInterval(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ action: 'ping' }));
            } else {
                clearInterval(heartbeat);
            }
        }, 30000); // 每30秒发送一次心跳
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            switch (data.type) {
                case 'full_state':
                    dom.loader.classList.add('hidden'); document.body.style.overflow = 'auto';
                    updateStatusText(data.status, data.current_category);
                    setAppBusyState(data.is_crawling, data.is_organizing);
                    updateStats(data.stats); updateProgress(data.progress);
                    dom.concurrencySlider.value = data.concurrency; dom.concurrencyValue.textContent = data.concurrency;
                    dom.pageConcurrencySlider.value = data.page_concurrency; dom.pageConcurrencyValue.textContent = data.page_concurrency;
                    break;
                case 'log': addLog(data.html, data.plain); break;
                case 'state_update':
                    if (data.status !== undefined || data.current_category !== undefined) updateStatusText(data.status, data.current_category);
                    if (data.is_crawling !== undefined || data.is_organizing !== undefined) setAppBusyState(data.is_crawling, data.is_organizing);
                    if (data.stats) updateStats(data.stats); if (data.progress) updateProgress(data.progress);
                    if (data.concurrency) { dom.concurrencySlider.value = data.concurrency; dom.concurrencyValue.textContent = data.concurrency; }
                    if (data.page_concurrency) { dom.pageConcurrencySlider.value = data.page_concurrency; dom.pageConcurrencyValue.textContent = data.page_concurrency; }
                    break;
                case 'categories_list':
                    dom.categoryChecklist.innerHTML = '';
                    data.categories.forEach(c => { const id = `cat-${c}`; dom.categoryChecklist.innerHTML += `<label for="${id}"><input type="checkbox" id="${id}" value="${c}"> ${c}</label>`; });
                    break;

            }
        };
    };
    
    const setupEventListeners = () => {
        dom.tabs.forEach(tab => tab.addEventListener('click', () => { dom.tabs.forEach(t => t.classList.remove('active')); dom.tabContents.forEach(c => c.classList.remove('active')); tab.classList.add('active'); $(`#${tab.dataset.tab}`).classList.add('active'); }));
        dom.startBtn.addEventListener('click', () => {
            const selected = Array.from($$('#category-checklist input:checked')).map(cb => cb.value);
            if (selected.length === 0) return addLog('<span style="color:var(--warning-color)">请至少选择一个分类！</span>', '请至少选择一个分类！');
            // 开始新任务前清理UI状态
            clearUIState();
            ws.send(JSON.stringify({ action: 'start_crawl', categories: selected}));
        });
        dom.stopBtn.addEventListener('click', () => {
            ws.send(JSON.stringify({ action: 'stop_crawl' }));
            // 清理UI状态
            clearUIState();
        });
        dom.organizeBtn.addEventListener('click', () => { ws.send(JSON.stringify({ action: 'organize_files' })) });
        dom.concurrencySlider.addEventListener('input', e => { dom.concurrencyValue.textContent = e.target.value; });
        dom.concurrencySlider.addEventListener('change', e => { ws.send(JSON.stringify({ action: 'set_concurrency', value: parseInt(e.target.value) })) });
        dom.pageConcurrencySlider.addEventListener('input', e => { dom.pageConcurrencyValue.textContent = e.target.value; });
        dom.pageConcurrencySlider.addEventListener('change', e => { ws.send(JSON.stringify({ action: 'set_page_concurrency', value: parseInt(e.target.value) })) });

    };

    document.addEventListener('DOMContentLoaded', () => { setupEventListeners(); connectWebSocket(); });
})();
</script>
</body>
</html>
"""

# --- Python Backend (Error Logging) ---
def clean_filename(text: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', '_', text).strip()

async def init_db():
    async with aiosqlite.connect(DB_FILE) as db:
        await db.execute("""
            CREATE TABLE IF NOT EXISTS processed_urls (
                url TEXT PRIMARY KEY,
                album_title TEXT NOT NULL,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        await db.commit()
    old_state_file = 'download_state.json'
    if os.path.exists(old_state_file):
        logger.info(f"发现旧的状态文件 '{old_state_file}'，开始迁移到SQLite数据库...")
        try:
            with open(old_state_file, 'r', encoding='utf-8') as f: data = json.load(f)
            if isinstance(data, dict) and 'processed_urls' in data:
                urls_to_migrate = [(url, status) for url, status in data['processed_urls'].items() if status not in ["Untitled", "unknown"]]
                if urls_to_migrate:
                    async with aiosqlite.connect(DB_FILE) as db:
                        await db.executemany("INSERT OR IGNORE INTO processed_urls (url, album_title) VALUES (?, ?)", urls_to_migrate)
                        await db.commit()
                    logger.success(f"成功迁移 {len(urls_to_migrate)} 条URL记录到数据库。")
            os.rename(old_state_file, f"{old_state_file}.migrated")
            logger.info(f"旧状态文件已重命名为 '{old_state_file}.migrated'。")
        except Exception as e:
            logger.error(f"从JSON文件迁移数据失败: {e}")

async def get_processed_urls_from_db() -> set:
    async with aiosqlite.connect(DB_FILE) as db:
        async with db.execute("SELECT url FROM processed_urls") as cursor:
            return {row[0] for row in await cursor.fetchall()}

async def add_url_to_db(url: str, album_title: str):
    async with aiosqlite.connect(DB_FILE) as db:
        await db.execute("INSERT OR REPLACE INTO processed_urls (url, album_title) VALUES (?, ?)", (url, album_title))
        await db.commit()

# 【新增】函数：记录失败的图集到JSON文件
async def log_failed_album(url: str, title: str, reason: str):
    async with error_log_lock:
        error_entry = {
            "url": url,
            "title": title,
            "reason": reason,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        try:
            if os.path.exists(ERROR_LOG_FILE):
                async with aiofiles.open(ERROR_LOG_FILE, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    errors = json.loads(content) if content else []
            else:
                errors = []
            
            # 检查是否已记录过该URL
            if not any(e['url'] == url for e in errors):
                errors.append(error_entry)
                async with aiofiles.open(ERROR_LOG_FILE, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(errors, indent=4, ensure_ascii=False))
        except Exception as e:
            logger.error(f"写入错误日志 '{ERROR_LOG_FILE}' 失败: {e}")


async def update_shared_state(data_to_update: Dict):
    async with shared_state['lock']:
        for key, value in data_to_update.items():
            if isinstance(shared_state.get(key), dict) and isinstance(value, dict):
                shared_state[key].update(value)
            else:
                shared_state[key] = value
        # 非阻塞状态更新
        try:
            broadcast_queue.put_nowait({"type": "state_update", **data_to_update})
        except asyncio.QueueFull:
            pass  # 状态更新可以丢失，不影响核心功能

def parse_page_for_links(html_content: str, base_url: str) -> Tuple[List[Dict[str, str]], Optional[int]]:
    soup = BeautifulSoup(html_content, 'lxml')
    links, max_page = [], None
    for a in soup.select('a.thumbnail-link'):
        img = a.find('img')
        if img and not (a.find_parent('div', class_='post_thumb post_thumb_top') or (img.get('width') == '360' and img.get('height') == '540')):
            title = img.get('title')
            if not title or title.strip() == '':
                slug = a['href'].strip('/').split('/')[-1]
                decoded_slug = unquote(slug)
                title = decoded_slug.replace('-', ' ').replace('_', ' ').title()
            links.append({'url': urljoin(base_url, a['href']), 'title': title})
    page_numbers = soup.select('a.page-numbers')
    if page_numbers:
        nums = [int(p.text) for p in page_numbers if p.text.isdigit()]
        if nums:
            max_page = max(nums)
    return links, max_page

# ##########################################################################
# ######################### 【核心修改区域】 ###############################
# ##########################################################################

def parse_album_for_images(html_content: str) -> List[str]:
    """
    解析图集页面以提取所有图片链接。
    【已修改】此函数现在可以处理不带文件扩展名的图片URL。
    """
    soup = BeautifulSoup(html_content, 'lxml')
    urls = set()
    
    # 【已修改】使用更通用的后代选择器 (空格代替 >) 并移除了对文件扩展名的严格检查。
    # 这可以匹配您提供的HTML结构: <div class="separator"><a><img></a></div>
    # 同时也兼容其他可能的结构，如 WordPress 的 <figure class="wp-block-image">
    for img in soup.select('div.separator img, figure.wp-block-image img'):
        src = img.get('src') or img.get('data-src')
        
        # 【已修改】只要 src 属性存在且不为空，就认为它是有效的图片链接。
        if src:
            urls.add(src)
            
    return sorted(list(urls))

# ##########################################################################
# ######################### 【修改结束】 ###################################
# ##########################################################################


async def get_html_with_pyppeteer(url: str) -> str:
    if not shared_state.get("browser"): return ""
    page = None
    try:
        page = await shared_state["browser"].newPage()
        await page.goto(url, timeout=PYPPETEER_PAGE_TIMEOUT)
        # 等待JS执行，可以根据需要调整延时
        await asyncio.sleep(PYPPETEER_WAIT_DELAY)
        content = await page.content()
        return content
    except Exception as e:
        logger.error(f"Pyppeteer获取页面 {url} 失败: {e}")
        return ""
    finally:
        if page: await page.close()


async def download_image(url: str, client: httpx.AsyncClient, image_semaphore: asyncio.Semaphore, folder_path: str, album_name: str) -> Tuple[str, int]:
    filename = os.path.join(folder_path, f'_{url.split("/")[-1]}')
    try:
        if await aiofiles.os.path.exists(filename) and await aiofiles.os.path.getsize(filename) > 0:
            await stats_update_queue.put({'album_name': album_name, 'status': 'skipped', 'size': 0})
            return 'skipped', 0
    except OSError:
        pass

    async with image_semaphore:
        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return 'cancelled', 0

        for attempt in range(3):
            try:
                # 在每次重试前检查停止标志
                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                    return 'cancelled', 0

                temp_file = filename + '.part'
                async with client.stream("GET", url, timeout=HTTP_TIMEOUT, follow_redirects=True) as r:
                    r.raise_for_status()
                    size = int(r.headers.get('content-length', 0))
                    async with aiofiles.open(temp_file, 'wb') as f:
                        async for chunk in r.aiter_bytes(65536):
                            # 在下载过程中检查停止标志
                            if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                return 'cancelled', 0
                            await f.write(chunk)

                await aiofiles.os.rename(temp_file, filename)
                await stats_update_queue.put({'album_name': album_name, 'status': 'ok', 'size': size})
                return 'ok', size
            except asyncio.CancelledError:
                # 清理临时文件
                try:
                    if await aiofiles.os.path.exists(temp_file):
                        await aiofiles.os.remove(temp_file)
                except:
                    pass
                return 'cancelled', 0
            except Exception as e:
                if attempt < 2:  # 不是最后一次重试
                    await asyncio.sleep(2)
                else:
                    logger.error(f"下载图片 {url} 失败: {e}")

    await stats_update_queue.put({'album_name': album_name, 'status': 'failed', 'size': 0})
    return 'failed', 0

async def download_album(page_info: Dict, category: str, client: httpx.AsyncClient, image_concurrency: int, state_lock: asyncio.Lock) -> Tuple[bool, bool, str]:
    # 检查停止标志
    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
        return False, False, ""

    album_name = clean_filename(page_info['title'])

    try:
        # 在网络请求前检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False, False, album_name

        resp = await client.get(page_info['url'], timeout=HTTP_TIMEOUT, follow_redirects=True)
        resp.raise_for_status()
        html_content = resp.text

        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False, False, album_name

        soup = BeautifulSoup(html_content, 'lxml')
        header_tag = soup.select_one('h1.entry-title, h2.single-post-title')
        if header_tag and header_tag.text.strip():
            album_name = clean_filename(header_tag.text.strip())

        folder_path = os.path.join(SAVE_DIR, category, album_name)
        if await aiofiles.os.path.exists(os.path.join(folder_path, '.download_complete')):
             await add_url_to_db(page_info['url'], album_name)
             return True, False, album_name

        urls = parse_album_for_images(html_content)

        if not urls and PYPPETEER_AVAILABLE and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
            await log_to_ui(f"图集 [<b>{album_name}</b>] 初始解析失败, 启动浏览器模式重试...", m_type="warning")
            html_content = await get_html_with_pyppeteer(page_info['url'])
            if html_content:
                urls = parse_album_for_images(html_content)

        if not urls:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 最终未能解析到任何有效图片链接，跳过。", m_type="error")
            await log_failed_album(page_info['url'], album_name, "解析失败") # 【修改】记录解析失败
            return False, False, album_name

        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return False, False, album_name

        await aiofiles.os.makedirs(folder_path, exist_ok=True)
        await log_to_ui(f"图集 [<b>{album_name}</b>]: 发现 {len(urls)} 张新图", m_type="primary")
        async with state_lock:
            shared_state['progress'][album_name] = {'completed': 0, 'total': len(urls)}

        image_semaphore = asyncio.Semaphore(image_concurrency)
        tasks = []
        for url in urls:
            if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                break
            task = asyncio.create_task(download_image(url, client, image_semaphore, folder_path, album_name))
            tasks.append(task)
            active_tasks.add(task)

        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except asyncio.CancelledError:
            logger.info(f"图集 {album_name} 下载被取消")
            return False, False, album_name
        finally:
            # 清理任务
            for task in tasks:
                active_tasks.discard(task)
                if not task.done():
                    task.cancel()

        success_count = sum(1 for res in results if isinstance(res, tuple) and res[0] in ('ok', 'skipped'))

        async with state_lock:
            if album_name in shared_state['progress']:
                del shared_state['progress'][album_name]

        if success_count == len(urls):
            async with aiofiles.open(os.path.join(folder_path, '.download_complete'), 'w') as f:
                await f.write(time.strftime("%Y-%m-%d %H:%M:%S"))
            await add_url_to_db(page_info['url'], album_name)
            return True, True, album_name
        else:
            await log_to_ui(f"图集 [<b>{album_name}</b>] 部分失败", m_type="warning")
            await log_failed_album(page_info['url'], album_name, "部分下载失败") # 【修改】记录部分下载失败
            return False, False, album_name
    except asyncio.CancelledError:
        logger.info(f"图集 {album_name} 下载被取消")
        async with state_lock:
            if album_name in shared_state['progress']:
                del shared_state['progress'][album_name]
        return False, False, album_name
    except Exception as e:
        logger.error(f"处理图集 '{album_name}' ({page_info['url']}) 失败: {e}")
        async with state_lock:
            if album_name in shared_state['progress']:
                del shared_state['progress'][album_name]
        await log_failed_album(page_info['url'], album_name, f"下载任务异常: {e}") # 【修改】记录异常
        return False, False, album_name

async def stats_aggregator_task():
    bytes_this_sec, last_update = 0, time.monotonic()
    try:
        while CRAWL_RUNNING.is_set() or not stats_update_queue.empty():
            try:
                item = await asyncio.wait_for(stats_update_queue.get(), timeout=1.0)
                status, size, name = item.get('status'), item.get('size', 0), item.get('album_name')
                async with shared_state['lock']:
                    if name in shared_state['progress']:
                        shared_state['progress'][name]['completed'] += 1
                    if status == 'ok':
                        shared_state['stats']['totalImages'] += 1
                        shared_state['stats']['totalSize'] += size
                        bytes_this_sec += size
                stats_update_queue.task_done()
            except asyncio.TimeoutError:
                pass
            except asyncio.CancelledError:
                break

            if (delta := time.monotonic() - last_update) >= 1.0:
                speed = bytes_this_sec / delta
                bytes_this_sec, last_update = 0, time.monotonic()
                async with shared_state['lock']:
                    shared_state['stats']['speed'] = speed
                    state_copy = {"progress": json.loads(json.dumps(shared_state['progress'])), "stats": json.loads(json.dumps(shared_state['stats']))}
                # 非阻塞统计更新
                try:
                    broadcast_queue.put_nowait({"type": "state_update", **state_copy})
                except asyncio.QueueFull:
                    pass  # 统计更新可以丢失

            # 检查强制停止标志
            if FORCE_STOP.is_set():
                break
    except asyncio.CancelledError:
        logger.info("统计聚合任务被取消")
    finally:
        await update_shared_state({"stats": {"speed": 0.0}})

async def album_downloader_worker(queue: asyncio.Queue, cat_name: str, client: httpx.AsyncClient, image_concurrency: int, state_lock: asyncio.Lock):
    try:
        while CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
            try:
                # 使用超时等待，以便能够响应停止信号
                page_info = await asyncio.wait_for(queue.get(), timeout=1.0)
                if page_info is None:
                    break

                success, new_dl, _ = await download_album(page_info, cat_name, client, image_concurrency, state_lock)
                if success and new_dl:
                    async with state_lock:
                        shared_state['stats']['totalAlbums'] += 1
                    # 使用 update_shared_state 来确保正确广播
                    await update_shared_state({"stats": shared_state['stats'].copy()})
                queue.task_done()
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环检查停止标志
                continue
            except asyncio.CancelledError:
                logger.info(f"下载器 worker for {cat_name} 被取消")
                break
    except Exception as e:
        logger.error(f"下载器 worker for {cat_name} 异常退出: {e}")
    finally:
        # 确保队列任务被标记为完成
        try:
            while not queue.empty():
                queue.task_done()
        except ValueError:
            pass

async def scan_category_page(url: str, client: httpx.AsyncClient, processed_urls: set, album_queue: asyncio.Queue, page_num: int, cat_name: str):
    try:
        # 检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return

        resp = await client.get(url, follow_redirects=True)
        resp.raise_for_status()

        # 再次检查停止标志
        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
            return

        pages, _ = parse_page_for_links(resp.text, url)
        new_pages = [p for p in pages if p['url'] not in processed_urls]
        await log_to_ui(f"并发扫描 <b>{cat_name}</b> 第 {page_num} 页... 新增 {len(new_pages)} 个")
        for page in new_pages:
            if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                break
            await album_queue.put(page)
    except asyncio.CancelledError:
        logger.info(f"扫描页面 {url} 被取消")
    except Exception as e:
        logger.error(f"并发扫描页面 {url} 失败: {e}")

async def crawl_task(categories_to_crawl: List[str]):
    task = asyncio.current_task()
    active_tasks.add(task)
    aggregator = None
    browser_instance = None

    try:
        CRAWL_RUNNING.set()
        FORCE_STOP.clear()  # 重置强制停止标志
        await update_shared_state({"status": "运行中", "is_crawling": True})
        await log_to_ui("爬虫任务启动...", m_type="success")

        if PYPPETEER_AVAILABLE and not shared_state.get("browser"):
            await log_to_ui("正在启动浏览器实例...", m_type="primary")
            try:
                browser_instance = await pyppeteer.launch(headless=True, args=['--no-sandbox'])
                shared_state["browser"] = browser_instance
            except Exception as e:
                logger.error(f"启动Pyppeteer失败: {e}")
                await log_to_ui("启动浏览器失败，将不使用动态解析功能。", m_type="error")

        aggregator = asyncio.create_task(stats_aggregator_task())
        active_tasks.add(aggregator)

        processed_urls = await get_processed_urls_from_db()

        async with shared_state['lock']:
            image_concurrency = shared_state['concurrency']
            page_concurrency = shared_state['page_concurrency']
            state_lock = shared_state['lock']

        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'}

        try:
            async with httpx.AsyncClient(http2=True, verify=False, headers=headers, timeout=HTTP_TIMEOUT) as client:
                album_queue = asyncio.Queue(maxsize=MAX_ALBUM_QUEUE_SIZE)

                # 如果选择了多个分类，减少每个分类的并发数以避免资源竞争
                concurrent_albums = MAX_CONCURRENT_ALBUMS
                if len(categories_to_crawl) > 2:
                    concurrent_albums = max(5, MAX_CONCURRENT_ALBUMS // len(categories_to_crawl))
                    await log_to_ui(f"检测到 {len(categories_to_crawl)} 个分类，调整单分类并发数为 {concurrent_albums}", m_type="info")

                for cat_name in categories_to_crawl:
                    if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                        break
                    base_url = CATEGORIES.get(cat_name)
                    if not base_url:
                        continue

                    await update_shared_state({"current_category": cat_name})
                    await aiofiles.os.makedirs(os.path.join(SAVE_DIR, cat_name), exist_ok=True)
                    await log_to_ui(f"--- 开始处理分类: <b>{cat_name}</b> ---", m_type="primary")

                    # 创建下载器任务
                    downloader_tasks = []
                    for _ in range(concurrent_albums):
                        task = asyncio.create_task(album_downloader_worker(album_queue, cat_name, client, image_concurrency, state_lock))
                        downloader_tasks.append(task)
                        active_tasks.add(task)

                    try:
                        if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                            break

                        resp = await client.get(base_url, follow_redirects=True)
                        resp.raise_for_status()
                        pages, max_pages = parse_page_for_links(resp.text, base_url)

                        if max_pages and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                            await log_to_ui(f"<b>{cat_name}</b>: 检测到 {max_pages} 页, 开始并发扫描...", m_type="info")
                            new_pages = [p for p in pages if p['url'] not in processed_urls]
                            for page in new_pages:
                                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                    break
                                await album_queue.put(page)

                            scan_tasks = []
                            semaphore = asyncio.Semaphore(page_concurrency)

                            async def throttled_scan(page_num):
                                async with semaphore:
                                    if CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                                        page_url = f"{base_url}page/{page_num}/"
                                        await scan_category_page(page_url, client, processed_urls, album_queue, page_num, cat_name)

                            for page_num in range(2, max_pages + 1):
                                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                    break
                                task = asyncio.create_task(throttled_scan(page_num))
                                scan_tasks.append(task)
                                active_tasks.add(task)

                            # 等待扫描任务完成
                            if scan_tasks:
                                try:
                                    await asyncio.gather(*scan_tasks)
                                except asyncio.CancelledError:
                                    logger.info("扫描任务被取消")
                                finally:
                                    # 清理扫描任务
                                    for task in scan_tasks:
                                        active_tasks.discard(task)
                                        if not task.done():
                                            task.cancel()
                        else:
                            await log_to_ui(f"<b>{cat_name}</b>: 未检测到分页，仅处理第一页。", m_type="warning")
                            for p in pages:
                                if not CRAWL_RUNNING.is_set() or FORCE_STOP.is_set():
                                    break
                                await album_queue.put(p)
                    except Exception as e:
                        logger.error(f"扫描分类 {cat_name} 首页失败: {e}")

                    # 等待队列处理完成或停止信号
                    try:
                        while not album_queue.empty() and CRAWL_RUNNING.is_set() and not FORCE_STOP.is_set():
                            await asyncio.sleep(0.1)
                    except asyncio.CancelledError:
                        pass

                    # 发送停止信号给下载器
                    for _ in range(concurrent_albums):
                        await album_queue.put(None)

                    # 等待下载器任务完成
                    try:
                        await asyncio.gather(*downloader_tasks, return_exceptions=True)
                    except Exception as e:
                        logger.error(f"等待下载器任务完成时出错: {e}")
                    finally:
                        # 清理下载器任务
                        for task in downloader_tasks:
                            active_tasks.discard(task)
                            if not task.done():
                                task.cancel()

        except Exception as e:
            logger.error(f"爬取任务异常: {e}")
        finally:
            # 清理浏览器实例
            if browser_instance:
                try:
                    await log_to_ui("正在关闭浏览器实例...", m_type="primary")
                    await browser_instance.close()
                except Exception as e:
                    logger.error(f"关闭浏览器失败: {e}")
                finally:
                    shared_state["browser"] = None

        if FORCE_STOP.is_set():
            await log_to_ui("爬虫任务被强制停止!", m_type="warning")
        else:
            await log_to_ui("所有任务完成!", m_type="success")

    except asyncio.CancelledError:
        await log_to_ui("爬虫任务被取消!", m_type="warning")
    except Exception as e:
        logger.error(f"爬取任务异常: {e}")
        await log_to_ui(f"爬虫任务异常: {e}", m_type="error")
    finally:
        # 重置速度并更新状态
        async with shared_state['lock']:
            shared_state['stats']['speed'] = 0.0
        await update_shared_state({"status": "空闲", "is_crawling": False, "current_category": "", "progress": {}, "stats": shared_state['stats'].copy()})
        CRAWL_RUNNING.clear()
        active_tasks.discard(task)

        # 清理聚合器任务
        if aggregator:
            active_tasks.discard(aggregator)
            if not aggregator.done():
                aggregator.cancel()
                try:
                    await aggregator
                except asyncio.CancelledError:
                    pass

async def organize_files_task():
    await update_shared_state({"status": "整理中...", "is_organizing": True})
    await log_to_ui("整理功能暂未完全适配数据库，可能导致状态不一致。请谨慎使用。", m_type="warning")
    await update_shared_state({"status": "整理完成", "is_organizing": False})

async def broadcaster():
    while True:
        try:
            msg = await asyncio.wait_for(broadcast_queue.get(), timeout=0.2)
            await ws_manager.broadcast(msg)
            broadcast_queue.task_done()
        except asyncio.TimeoutError: pass

def get_initial_stats_sync():
    stats = {'totalAlbums': 0, 'totalImages': 0, 'totalSize': 0}
    if not os.path.exists(SAVE_DIR): return stats
    for root, dirs, _ in os.walk(SAVE_DIR):
        dirs[:] = [d for d in dirs if d not in CATEGORIES]
        for album in dirs:
            album_path = os.path.join(root, album)
            if os.path.exists(os.path.join(album_path, '.download_complete')):
                stats['totalAlbums'] += 1
                for f in os.listdir(album_path):
                    if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp')) or not '.' in f: # 包含无扩展名的文件
                        try:
                            stats['totalImages'] += 1
                            stats['totalSize'] += os.path.getsize(os.path.join(album_path, f))
                        except OSError: pass
    return stats



@app.on_event("startup")
async def startup_tasks():
    await init_db()
    asyncio.create_task(broadcaster())
    if not PYPPETEER_AVAILABLE:
        logger.warning("Pyppeteer未安装, 动态内容解析功能将不可用。请运行 'pip install pyppeteer'。")

@app.get("/", response_class=HTMLResponse)
async def get_root(): return HTML_TEMPLATE



async def get_current_app_state() -> Dict:
    async with shared_state['lock']: return json.loads(json.dumps({k: v for k, v in shared_state.items() if k not in ['lock', 'browser']}))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await ws_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            action = data.get('action')
            if action == "get_initial_state":
                await websocket.send_json({"type": "categories_list", "categories": list(CATEGORIES.keys())})
                stats = await asyncio.to_thread(get_initial_stats_sync)
                async with shared_state['lock']: shared_state['stats'].update(stats)
                state = await get_current_app_state()
                await websocket.send_json({"type": "full_state", **state})
            else:
                async with shared_state['lock']: is_busy = shared_state['is_crawling'] or shared_state['is_organizing']
                if action == "start_crawl" and not is_busy:
                    asyncio.create_task(crawl_task(data.get('categories', [])))
                elif action == "stop_crawl" and shared_state['is_crawling']:
                    await log_to_ui("收到停止指令，正在安全停止所有任务...", m_type="warning")
                    CRAWL_RUNNING.clear()

                    # 给任务一些时间自然停止
                    await asyncio.sleep(1)

                    # 如果任务还在运行，强制取消
                    if shared_state['is_crawling']:
                        await log_to_ui("强制取消剩余任务...", m_type="warning")
                        FORCE_STOP.set()

                        # 取消所有活跃任务
                        for task in active_tasks.copy():
                            if not task.done():
                                task.cancel()

                        # 等待任务清理
                        await asyncio.sleep(2)

                        # 强制更新状态，并清空下载队列，重置速度
                        async with shared_state['lock']:
                            shared_state['stats']['speed'] = 0.0
                        await update_shared_state({"status": "已停止", "is_crawling": False, "current_category": "", "progress": {}, "stats": shared_state['stats'].copy()})
                elif action == "organize_files" and not is_busy:
                    asyncio.create_task(organize_files_task())
                elif action == "set_concurrency" and not is_busy:
                    await update_shared_state({'concurrency': int(data.get('value', DEFAULT_IMAGE_CONCURRENCY))})
                elif action == "set_page_concurrency" and not is_busy:
                    await update_shared_state({'page_concurrency': int(data.get('value', PAGE_SCAN_CONCURRENCY))})

                elif action == "ping":
                    # 心跳响应
                    await websocket.send_json({"type": "pong"})
    except WebSocketDisconnect: logger.info("客户端断开连接")
    except Exception as e: logger.warning(f"WebSocket 错误: {e}")
    finally: ws_manager.disconnect(websocket)

if __name__ == "__main__":
    print("="*60)
    print(" Everia Crawler V16.5 (UI Fix)")
    print("="*60)
    print(" 启动成功! 请在浏览器中打开 http://127.0.0.1:8001")
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="warning")